import { useState, useEffect, useCallback } from 'react';
import mangaService from '../services/manga-service';

export interface MangaData {
    id: string;
    title: string;
    coverUrl: string;
    views?: number;
    loves?: number;
}

export type TabType = 'top' | 'favorite' | 'new';

export interface TabInfo {
    data: MangaData[];
    icon: React.ReactNode;
    title: string;
    statIcon: React.ReactNode | null;
    statValue: (manga: MangaData) => string;
}

export const useTopManga = () => {
    // State cho dữ liệu manga
    const [topViewMangas, setTopViewMangas] = useState<MangaData[]>([]);
    const [topLoveMangas, setTopLoveMangas] = useState<MangaData[]>([]);
    const [newMangas, setNewMangas] = useState<MangaData[]>([]);
    
    // State cho UI
    const [activeTab, setActiveTab] = useState<TabType>('top');
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    // Các icon được memoized
    const icons = {
        view: (
            <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 576 512" className="text-blue-500" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg">
                <path d="M572.52 241.4C518.29 135.59 410.93 64 288 64S57.68 135.64 3.48 241.41a32.35 32.35 0 0 0 0 29.19C57.71 376.41 165.07 448 288 448s230.32-71.64 284.52-177.41a32.35 32.35 0 0 0 0-29.19zM288 400a144 144 0 1 1 144-144 143.93 143.93 0 0 1-144 144zm0-240a95.31 95.31 0 0 0-25.31 3.79 47.85 47.85 0 0 1-66.9 66.9A95.78 95.78 0 1 0 288 160z"></path>
            </svg>
        ),
        love: (
            <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 512 512" className="text-red-500" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg">
                <path d="M462.3 62.6C407.5 15.9 326 24.3 275.7 76.2L256 96.5l-19.7-20.3C186.1 24.3 104.5 15.9 49.7 62.6c-62.8 53.6-66.1 149.8-9.9 207.9l193.5 199.8c12.5 12.9 32.8 12.9 45.3 0l193.5-199.8c56.3-58.1 53-154.3-9.8-207.9z"></path>
            </svg>
        ),
        new: (
            <svg stroke="currentColor" fill="currentColor" strokeWidth="0" viewBox="0 0 512 512" className="text-green-500" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg">
                <path d="M256,8C119,8,8,119,8,256S119,504,256,504,504,393,504,256,393,8,256,8Zm92.49,313h0l-20,25a16,16,0,0,1-22.49,2.5h0l-67-49.72a40,40,0,0,1-15-31.23V112a16,16,0,0,1,16-16h32a16,16,0,0,1,16,16V256l58,42.5A16,16,0,0,1,348.49,321Z"></path>
            </svg>
        )
    };

    // Fetch tất cả dữ liệu ranking
    const fetchAllRankings = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);

            // Fetch song song để tối ưu performance
            const [topViewsResult, topLovesResult, newMangasResult] = await Promise.all([
                mangaService.getMangaSummaries(0, 5, "views,desc"),
                mangaService.getMangaSummaries(0, 5, "loves,desc"),
                mangaService.getMangaSummaries(0, 5, "createdAt,desc")
            ]);

            // Cập nhật state
            if (topViewsResult) {
                setTopViewMangas(topViewsResult.content);
            }
            if (topLovesResult) {
                setTopLoveMangas(topLovesResult.content);
            }
            if (newMangasResult) {
                setNewMangas(newMangasResult.content);
            }

        } catch (error) {
            console.error('Lỗi khi tải bảng xếp hạng:', error);
            setError('Không thể tải dữ liệu bảng xếp hạng');
        } finally {
            setLoading(false);
        }
    }, []);

    // Load dữ liệu khi component mount
    useEffect(() => {
        fetchAllRankings();
    }, [fetchAllRankings]);

    // Lấy thông tin tab đang active
    const getActiveTabData = useCallback((): TabInfo => {
        switch (activeTab) {
            case 'top':
                return {
                    data: topViewMangas,
                    icon: icons.view,
                    title: "Top lượt xem",
                    statIcon: icons.view,
                    statValue: (manga: MangaData) => `${manga.views?.toLocaleString() || 0} lượt xem`
                };
            case 'favorite':
                return {
                    data: topLoveMangas,
                    icon: icons.love,
                    title: "Top yêu thích",
                    statIcon: icons.love,
                    statValue: (manga: MangaData) => `${manga.loves?.toLocaleString() || 0} lượt thích`
                };
            case 'new':
                return {
                    data: newMangas,
                    icon: icons.new,
                    title: "Truyện mới",
                    statIcon: null,
                    statValue: () => "Mới cập nhật"
                };
            default:
                return {
                    data: topViewMangas,
                    icon: icons.view,
                    title: "Top lượt xem",
                    statIcon: icons.view,
                    statValue: (manga: MangaData) => `${manga.views?.toLocaleString() || 0} lượt xem`
                };
        }
    }, [activeTab, topViewMangas, topLoveMangas, newMangas, icons]);

    // Hàm refresh dữ liệu
    const refreshData = useCallback(() => {
        fetchAllRankings();
    }, [fetchAllRankings]);

    return {
        // Data
        topViewMangas,
        topLoveMangas,
        newMangas,
        
        // UI State
        activeTab,
        setActiveTab,
        loading,
        error,
        
        // Computed data
        activeTabInfo: getActiveTabData(),
        
        // Icons
        icons,
        
        // Actions
        refreshData
    };
};

export default useTopManga;
